# 通用批量任务框架设计方案 v1.0

## 1. 业界主流方案调研

### 1.1 Spring Batch
**核心特点**：
- Job -> Step -> ItemReader/ItemProcessor/ItemWriter 三层架构
- 支持事务管理、重试、跳过、重启等企业级特性
- 通过JobParameters实现参数化
- 元数据管理通过JobRepository

**优点**：成熟稳定、企业级特性完善、Spring生态集成好
**缺点**：学习曲线陡峭、配置复杂、不够轻量

### 1.2 XXL-JOB
**核心特点**：
- 分布式任务调度平台
- 支持多种执行模式：BEAN、GLUE、Shell等
- 路由策略：轮询、随机、一致性HASH等
- 失败处理：告警、重试

**优点**：轻量级、易用、分布式支持好
**缺点**：批量处理能力相对简单、缺乏复杂业务流程支持

### 1.3 Elastic-Job
**核心特点**：
- 分片执行支持
- 弹性扩容缩容
- 失效转移
- 作业分片策略

**优点**：分片能力强、高可用性好
**缺点**：配置复杂、学习成本高

### 1.4 阿里SchedulerX
**核心特点**：
- 支持多种任务类型：单机、广播、Map-Reduce、分片
- 可视化任务配置和监控
- 工作流编排能力

**优点**：功能全面、可视化好、企业级特性完善
**缺点**：商业产品、定制化程度低

## 2. 现有方案分析

### 2.1 优点分析
1. **完整的生命周期管理**：从接收请求到结果反馈的完整流程
2. **精细的状态控制**：任务表、时段表、执行表的多层状态管理
3. **并发控制机制**：基于时段的资源管理和并发控制
4. **容错处理**：支持重试、补偿、终止等异常处理
5. **文件处理能力**：完整的文件上传、解析、结果生成流程

### 2.2 可优化点
1. **通用性不足**：当前设计过于业务化，难以复用
2. **配置灵活性**：缺乏模板化配置机制
3. **扩展性限制**：新增任务类型需要修改核心代码
4. **监控能力**：缺乏完整的可观测性设计

## 3. 推荐的通用批量任务框架设计

### 3.1 整体架构

```plantuml
@startuml
!define RECTANGLE class

package "API层" {
  [任务提交API]
  [任务管理API]
  [监控查询API]
}

package "服务层" {
  [任务编排服务]
  [执行引擎服务]
  [监控告警服务]
}

package "执行层" {
  [XXL-JOB调度器]
  [任务执行器]
  [资源管理器]
  [状态管理器]
}

package "存储层" {
  [MySQL数据库]
  [Redis缓存]
  [文件存储]
  [消息队列]
  [Nacos配置中心]
}

[任务提交API] --> [任务编排服务]
[任务管理API] --> [执行引擎服务]
[监控查询API] --> [监控告警服务]

[任务编排服务] --> [XXL-JOB调度器]
[执行引擎服务] --> [任务执行器]
[监控告警服务] --> [状态管理器]

[XXL-JOB调度器] --> [MySQL数据库]
[任务执行器] --> [Redis缓存]
[资源管理器] --> [文件存储]
[状态管理器] --> [消息队列]
[任务编排服务] --> [Nacos配置中心]
@enduml
```

### 3.2 Nacos配置设计

#### 3.2.1 任务模板配置
**配置文件**：`qos-batch-templates.yml`
```yaml
batch-task:
  templates:
    # 批量默载调用模板
    batch_defbearer_call:
      name: "批量默载调用"
      description: "批量默载申请和调用"
      version: "1.0"
      # 文件处理Topic
      fileProcessTopic: "FILE_PROCESS_DEFBEARER_TOPIC"
      executor:
        class: "BatchDefbearerCallExecutor"
        method: "execute"
        # 执行模板 - 调用下游接口的JSON模板
        executeTemplate: |
          {
            "qosProductId": "${qosProductId}",
            "cmServiceId": "${cmServiceId}",
            "duration": "${duration}",
            "homeProvince": "${homeProvince}",
            "msisdn": "${msisdn}",
            "privateIp": "${privateIp}",
            "systemCode": "QOS_BATCH",
            "version": "1.0",
            "messageId": "${messageId}",
            "requestTime": "${requestTime}"
          }
      # 终止配置
      terminate:
        supportAfterComplete: true  # 执行完成后是否还支持终止
        needCallDownstream: true  # 是否需要调用下游终止接口
        class: "BatchDefbearerCallTerminator"
        method: "terminate"
        # 终止模板 - 调用下游终止接口的JSON模板
        terminateTemplate: |
          {
            "orderId": "${orderId}",
            "msisdn": "${msisdn}",
            "systemCode": "QOS_BATCH",
            "terminateReason": "用户主动终止",
            "requestTime": "${requestTime}"
          }

    # 批量销户模板
    batch_account_cancel:
      name: "批量销户"
      description: "批量用户销户"
      version: "1.0"
      # 文件处理Topic
      fileProcessTopic: "FILE_PROCESS_ACCOUNT_TOPIC"
      executor:
        class: "BatchAccountCancelExecutor"
        method: "execute"
        # 执行模板
        executeTemplate: |
          {
            "msisdn": "${msisdn}",
            "cancelType": "BATCH_CANCEL",
            "systemCode": "QOS_BATCH",
            "messageId": "${messageId}",
            "requestTime": "${requestTime}"
          }
      # 终止配置
      terminate:
        supportAfterComplete: false  # 执行完成后不支持终止
        needCallDownstream: false  # 销户成功的不需要调用下游
        class: "BatchAccountCancelTerminator"
        method: "terminate"
```

#### 3.2.2 并发配置（独立配置文件）
**配置文件**：`qos-batch-concurrency.yml`
```yaml
batch-task:
  concurrency:
    # 批量默载调用并发配置（混合配置）
    batch_defbearer_call:
      type: "province"  # 按省配置
      config:
        beijing: 60
        shanghai: 80
        guangdong: 100
        sichuan: 70
        default: 50      # 有省分编码但未配置的省份使用此配置
        no_province: 150 # 专门为无省分编码设置的并发

    # 批量销户并发配置
    batch_account_cancel:
      type: "global"  # 全局配置
      config:
        default: 50

    # 其他任务类型的并发配置...
    batch_other_task:
      type: "global"
      config:
        default: 30
```

#### 3.2.3 配置项字段说明

**任务模板配置字段说明**：

| 字段路径 | 类型 | 必填 | 说明 | 示例值 |
|---------|------|------|------|--------|
| `name` | String | 是 | 任务类型显示名称 | "批量默载调用" |
| `description` | String | 否 | 任务类型详细描述 | "批量默载申请和调用" |
| `version` | String | 是 | 模板版本号 | "1.0" |
| `fileProcessTopic` | String | 是 | 文件处理消息Topic | "FILE_PROCESS_DEFBEARER_TOPIC" |
| `executor.class` | String | 是 | 执行器类名 | "BatchDefbearerCallExecutor" |
| `executor.method` | String | 是 | 执行方法名 | "execute" |
| `executor.executeTemplate` | String | 是 | 执行模板JSON字符串 | 包含${}占位符的JSON |
| `terminate.supportAfterComplete` | Boolean | 是 | 执行完成后是否还支持终止 | true/false |
| `terminate.needCallDownstream` | Boolean | 是 | 是否需要调用下游终止接口 | true/false |
| `terminate.class` | String | 否 | 终止器类名（supportAfterComplete=true时必填） | "BatchDefbearerCallTerminator" |
| `terminate.method` | String | 否 | 终止方法名（supportAfterComplete=true时必填） | "terminate" |
| `terminate.terminateTemplate` | String | 否 | 终止模板JSON字符串（needCallDownstream=true时必填） | 包含${}占位符的JSON |

**并发配置字段说明**：

| 字段路径 | 类型 | 必填 | 说明 | 示例值 |
|---------|------|------|------|--------|
| `type` | String | 是 | 并发配置类型 | "province"（按省）/"global"（全局） |
| `config.{省份编码}` | Integer | 否 | 具体省份的并发数 | beijing: 60 |
| `config.default` | Integer | 是 | 默认并发数 | 50 |
| `config.no_province` | Integer | 否 | 无省分编码时的并发数（type=province时可选） | 150 |

**配置项使用说明**：

1. **terminate.supportAfterComplete**：
   - `true`：执行完成后还支持终止，"已执行"状态仍可以终止（如默载调用需要回滚）
   - `false`：执行完成后不支持终止，"已执行"状态不允许终止操作（如销户不可逆）
   - 注意：所有任务都支持执行完成前的终止，此字段仅区分执行完成后的情况

2. **terminate.needCallDownstream**：
   - `true`：终止时需要调用下游接口回滚已执行成功的数据
   - `false`：终止时仅标记状态，不调用下游接口

3. **并发配置优先级**：
   - 按省配置且有省分编码：使用具体省份配置 > default
   - 按省配置但无省分编码：使用no_province配置 > default
   - 全局配置：直接使用default

### 3.3 数据模型设计

#### 3.3.1 核心表结构
```sql
-- 批量任务主表
CREATE TABLE task.qos_batch_task (
  task_id BIGINT(20) NOT NULL COMMENT '任务ID',
  message_id VARCHAR(64) NOT NULL COMMENT '请求ID',
  task_type VARCHAR(64) NOT NULL COMMENT '任务类型',
  task_name VARCHAR(200) NOT NULL COMMENT '任务名称',
  task_parameters VARCHAR(2000) NOT NULL COMMENT '任务参数',
  home_province INT(11) COMMENT '归属省',
  file_path VARCHAR(500) COMMENT '输入文件路径',
  result_file_path VARCHAR(500) COMMENT '执行结果文件路径',
  terminate_file_path VARCHAR(500) COMMENT '终止结果文件路径',
  execute_count INT(11) DEFAULT NULL COMMENT '执行数量',
  req_expect_start_time DATETIME NOT NULL COMMENT '请求预置开始调用时间，格式为yyyy-MM-dd HH:mm:ss',
  expect_start_time DATETIME COMMENT '预期执行开始时间，格式为yyyy-MM-dd HH:mm:ss',
  expect_finish_time DATETIME COMMENT '预期执行完成时间，格式为yyyy-MM-dd HH:mm:ss',
  expect_result_notice_time DATETIME COMMENT '执行计划结果上报时间，格式为yyyy-MM-dd HH:mm:ss',
  rel_start_time DATETIME COMMENT '实际执行开始时间，格式为yyyy-MM-dd HH:mm:ss',
  rel_finish_time DATETIME COMMENT '实际执行完成时间，格式为yyyy-MM-dd HH:mm:ss',
  execute_result_notice_time DATETIME COMMENT '执行结果上报时间，格式为yyyy-MM-dd HH:mm:ss',
  expect_terminate_finish_time DATETIME DEFAULT NULL COMMENT '预期终止完成时间，格式为yyyy-MM-dd HH:mm:ss',
  rel_terminate_finish_time DATETIME DEFAULT NULL COMMENT '实际终止完成时间，格式为yyyy-MM-dd HH:mm:ss',
  status TINYINT(4) NOT NULL DEFAULT 0 COMMENT '状态 0-已接收请求待解析文件 1-待执行 2-执行完成待反馈执行结果 3-已执行 4-待终止 5-终止完成待反馈终止结果 6-已终止 7-解析失败',
  req_source VARCHAR(100) DEFAULT NULL COMMENT '请求来源',
  create_time DATETIME(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3) COMMENT '创建时间',
  update_time DATETIME(3) DEFAULT CURRENT_TIMESTAMP(3) ON UPDATE CURRENT_TIMESTAMP(3) COMMENT '修改时间',
  PRIMARY KEY (task_id),
  INDEX idx_task_type_status (task_type, status,home_province,req_expect_start_time),
  INDEX idx_rel_finish_time (rel_finish_time)
)ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 ROW_FORMAT=DYNAMIC COMMENT='批量任务主表';

-- 批量任务时段表
CREATE TABLE task.qos_batch_task_time_slot (
  task_id BIGINT(20) NOT NULL COMMENT '任务ID',
  slot_id INT(11) NOT NULL COMMENT '任务执行时段序号',
  home_province INT(11) COMMENT '归属省',
  slot_start_time DATETIME NOT NULL COMMENT '时段开始时间，格式为yyyy-MM-dd HH:mm:ss',
  slot_end_time DATETIME NOT NULL COMMENT '时段结束时间，格式为yyyy-MM-dd HH:mm:ss',
  execute_count INT(11) NOT NULL COMMENT '执行数量',
  status TINYINT(4) NOT NULL DEFAULT 0 COMMENT '状态 0-待执行 1-执行中 2-已完成 3-已终止',
  execute_time DATETIME COMMENT '执行时间',
  create_time DATETIME(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3) COMMENT '创建时间',
  update_time DATETIME(3) DEFAULT CURRENT_TIMESTAMP(3) ON UPDATE CURRENT_TIMESTAMP(3) COMMENT '修改时间',
  PRIMARY KEY (task_id, slot_id),
  INDEX idx_slot_time_status_province (slot_start_time, status, home_province)
)ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 ROW_FORMAT=DYNAMIC COMMENT='批量任务时段表';

-- 批量任务时段分片序号关联表
CREATE TABLE task.qos_batch_task_time_slot_shard_relation (
  task_id BIGINT NOT NULL COMMENT '任务ID',
  slot_id INT NOT NULL COMMENT '时段序号',
  shard_index INT NOT NULL COMMENT '分片序号',
  create_time DATETIME(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3) COMMENT '创建时间',
  PRIMARY KEY (task_id, slot_id, shard_index),
  INDEX idx_create_time (create_time)
)ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 ROW_FORMAT=DYNAMIC COMMENT='批量任务时段分片序号关联表';

-- 批量任务执行明细表（分16张表,按照business_key的hash分表）
CREATE TABLE task.qos_batch_task_execute_detail_0 (
  id int(11) NOT NULL COMMENT '序号，用作分片查询使用',
  task_id BIGINT(20) NOT NULL COMMENT '任务ID',
  business_key VARCHAR(100) NOT NULL COMMENT '业务主键（如手机号）',
  slot_id INT(11) COMMENT '时段序号',
  personalized_data VARCHAR(1000) COMMENT '个性化参数（JSON格式字符串，如私网IP等）',
  output_key_field VARCHAR(100) COMMENT '输出关键字段（如订单ID，用于终止调用）',
  output_data VARCHAR(2000) COMMENT '完整输出数据（JSON格式字符串，与输出关键字段不会同时有值）',
  status TINYINT(4) NOT NULL DEFAULT 0 COMMENT '状态 0-待执行 1-执行成功 2-执行失败 3-已终止',
  resp_code VARCHAR(11) COMMENT '响应码',
  resp_desc VARCHAR(255) COMMENT '响应描述',
  rel_start_time DATETIME COMMENT '实际开始时间',
  rel_terminate_time DATETIME COMMENT '实际终止时间',
  create_time DATETIME(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3) COMMENT '创建时间',
  update_time DATETIME(3) DEFAULT CURRENT_TIMESTAMP(3) ON UPDATE CURRENT_TIMESTAMP(3) COMMENT '修改时间',
  UNIQUE KEY uk_task_business (task_id, business_key),
  INDEX idx_task_slot_status (task_id, status, slot_id),
  INDEX idx_task_id (task_id, id)
)ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 ROW_FORMAT=DYNAMIC COMMENT='批量任务执行明细表';

```

#### 3.3.2 数据模型ER图

```plantuml
@startuml
!define TABLE(name,desc) class name as "desc" << (T,#FFAAAA) >>
!define PK(x) <b><color:#b8860b><&key></color> x</b>
!define FK(x) <color:#aaaaaa><&key></color> x
!define UNIQUE(x) <color:#green><&asterisk></color> x

' 定义表结构
TABLE(qos_batch_task, "批量任务主表\nqos_batch_task") {
  PK(task_id) : BIGINT(20)
  --
  message_id : VARCHAR(64)
  task_type : VARCHAR(64)
  task_name : VARCHAR(200)
  task_parameters : VARCHAR(2000)
  home_province : INT(11)
  file_path : VARCHAR(500)
  result_file_path : VARCHAR(500)
  terminate_file_path : VARCHAR(500)
  execute_count : INT(11)
  req_expect_start_time : DATETIME
  expect_start_time : DATETIME
  expect_finish_time : DATETIME
  expect_result_notice_time : DATETIME
  rel_start_time : DATETIME
  rel_finish_time : DATETIME
  execute_result_notice_time : DATETIME
  expect_terminate_finish_time : DATETIME
  rel_terminate_finish_time : DATETIME
  status : TINYINT(4)
  req_source : VARCHAR(100)
  create_time : DATETIME(3)
  update_time : DATETIME(3)
}

TABLE(qos_batch_task_time_slot, "批量任务时段表\nqos_batch_task_time_slot") {
  PK(task_id, slot_id) : BIGINT(20), INT(11)
  --
  FK(task_id) : BIGINT(20)
  home_province : INT(11)
  slot_start_time : DATETIME
  slot_end_time : DATETIME
  execute_count : INT(11)
  status : TINYINT(4)
  execute_time : DATETIME
  create_time : DATETIME(3)
  update_time : DATETIME(3)
}

TABLE(qos_batch_task_time_slot_shard_relation, "批量任务时段分片序号关联表\nqos_batch_task_time_slot_shard_relation") {
  PK(task_id, slot_id, shard_index) : BIGINT, INT, INT
  --
  FK(task_id) : BIGINT
  FK(slot_id) : INT
  shard_index : INT
  create_time : DATETIME(3)
}

TABLE(qos_batch_task_execute_detail_0, "批量任务执行明细表(分表0)\nqos_batch_task_execute_detail_0") {
  PK(id) : INT(11)
  --
  FK(task_id) : BIGINT(20)
  UNIQUE(business_key) : VARCHAR(100)
  slot_id : INT(11)
  personalized_data : VARCHAR(1000)
  output_key_field : VARCHAR(100)
  output_data : VARCHAR(2000)
  status : TINYINT(4)
  resp_code : VARCHAR(11)
  resp_desc : VARCHAR(255)
  rel_start_time : DATETIME
  rel_terminate_time : DATETIME
  create_time : DATETIME(3)
  update_time : DATETIME(3)
}

TABLE(qos_batch_task_execute_detail_N, "批量任务执行明细表(分表1-39)\nqos_batch_task_execute_detail_N") {
  PK(id) : INT(11)
  --
  FK(task_id) : BIGINT(20)
  UNIQUE(business_key) : VARCHAR(100)
  slot_id : INT(11)
  personalized_data : VARCHAR(1000)
  output_key_field : VARCHAR(100)
  output_data : VARCHAR(2000)
  status : TINYINT(4)
  resp_code : VARCHAR(11)
  resp_desc : VARCHAR(255)
  rel_start_time : DATETIME
  rel_terminate_time : DATETIME
  create_time : DATETIME(3)
  update_time : DATETIME(3)
}

' 定义关系
qos_batch_task ||--o{ qos_batch_task_time_slot : "1:N\n一个任务有多个时段"
qos_batch_task ||--o{ qos_batch_task_execute_detail_0 : "1:N\n一个任务有多个执行明细"
qos_batch_task ||--o{ qos_batch_task_execute_detail_N : "1:N\n一个任务有多个执行明细"
qos_batch_task_time_slot ||--o{ qos_batch_task_time_slot_shard_relation : "1:N\n一个时段对应多个分片"
qos_batch_task_time_slot ||--o{ qos_batch_task_execute_detail_0 : "1:N\n一个时段包含多个执行明细"
qos_batch_task_time_slot ||--o{ qos_batch_task_execute_detail_N : "1:N\n一个时段包含多个执行明细"

' 添加注释
note right of qos_batch_task : 任务主表\n存储任务基本信息、状态、时间等
note right of qos_batch_task_time_slot : 时段表\n存储任务的执行时段计划
note right of qos_batch_task_time_slot_shard_relation : 分片关联表\n记录时段与分片的对应关系
note right of qos_batch_task_execute_detail_0 : 执行明细表(分40张表)\n按business_key的hash分表\n存储具体的执行明细和结果
note bottom of qos_batch_task_execute_detail_N : 分表策略:\n表名后缀 = business_key.hashCode() % 40

@enduml
```

### 3.4 核心流程设计

#### 3.4.1 任务提交与调度流程

```plantuml
@startuml
participant "客户端" as client
participant "任务API" as api
participant "Nacos配置" as nacos
participant "任务服务" as task
participant "文件处理消费者" as consumer
participant "XXL-JOB" as xxljob
queue "文件处理Topic" as filetopic
queue "通用Topic" as commontopic
database "MySQL" as db

client -> api: 提交批量任务请求
api -> nacos: 获取模板配置
nacos --> api: 返回模板配置
api -> api: 参数校验

api -> task: 创建任务实例
task -> db: 插入任务实例记录（task_type字段）
task -> filetopic: 发送文件解析消息（按任务类型选择Topic）
task --> api: 返回任务ID

api --> client: 返回提交结果

filetopic -> consumer: 消费文件解析消息
consumer -> consumer: 根据任务类型选择解析方式
alt 自定义解析（batch_defbearer_call）
  loop 分批处理文件
    consumer -> consumer: 读取批次数据（号码+个性化参数）
    consumer -> db: 按business_key分表批量插入明细
  end
else 简单解析（其他任务类型）
  loop 分批处理文件
    consumer -> consumer: 读取批次数据（仅号码）
    consumer -> db: 按business_key分表批量插入明细
  end
end

consumer -> nacos: 获取并发配置类型
alt 按省配置
  consumer -> consumer: 使用省分锁（batch:lock:taskType:province）
else 全局配置
  consumer -> consumer: 使用任务类型锁（batch:lock:taskType:）
end
consumer -> consumer: 检查时段交叉并计算执行计划
consumer -> db: 插入执行计划记录
consumer -> xxljob: 按省或全局创建定时执行任务
consumer -> commontopic: 发送计划完成通知（通用Topic）

@enduml
```

#### 3.4.2 任务执行流程

```plantuml
@startuml
participant "XXL-JOB" as scheduler
participant "执行引擎" as engine
participant "执行器" as executor
participant "Nacos配置" as nacos
database "MySQL" as db
queue "通用Topic" as commontopic

scheduler -> engine: 触发任务执行（按省或分片参数）
engine -> db: 查询待执行时段（按省或分片）
engine -> nacos: 获取并发配置和执行模板

loop 处理每个时段
  engine -> db: 查询执行明细（按business_key查询40张分表）
  engine -> executor: 批量执行任务

  par 并发执行
    executor -> executor: 参数替换（个性化>通用>系统>表字段）
    executor -> executor: 调用下游接口
    executor -> db: 按business_key更新执行结果（output_key_field+output_data）
  end

  engine -> db: 更新时段状态
end

engine -> engine: 检查任务完成状态
alt 任务完成
  engine -> commontopic: 发送结果文件生成消息（通用Topic）
  engine -> db: 更新任务状态
else 任务未完成
  engine -> scheduler: 等待下次调度
end

@enduml
```

#### 3.4.3 任务终止流程

```plantuml
@startuml
participant "客户端" as client
participant "任务API" as api
participant "任务服务" as task
participant "Nacos配置" as nacos
participant "执行器" as executor
database "MySQL" as db
queue "通用Topic" as commontopic

client -> api: 请求终止任务
api -> db: 获取任务类型
api -> nacos: 获取终止配置
api -> api: 自定义校验逻辑（检查是否有成功数据）
api -> task: 验证终止条件
task -> db: 检查任务状态
task -> db: 更新任务状态为待终止
task -> db: 更新时段状态为终止执行
task -> commontopic: 发送终止消息（通用Topic）
task --> api: 返回终止结果

commontopic -> task: 消费终止消息
task -> task: 计算预期终止时间
task -> api: 通知预期终止时间

alt 需要调用下游终止接口（needCallDownstream=true）
  loop 终止执行
    task -> db: 按business_key查询执行成功的明细（查询40张分表）
    task -> nacos: 获取终止模板
    task -> executor: 调用终止逻辑
    executor -> executor: 使用output_key_field构建终止参数
    executor -> executor: 调用下游终止接口
    executor -> db: 按business_key更新终止结果（分表更新）
  end
else 仅标记终止（needCallDownstream=false）
  task -> db: 按business_key批量更新待执行数据为已终止（40张分表）
end

task -> task: 检查终止完成状态
task -> commontopic: 发送终止结果文件生成消息（通用Topic）
task -> db: 更新任务状态为已终止

@enduml
```

#### 3.4.4 通用处理流程总览

**主流程时序图**：
```plantuml
@startuml
participant "客户端" as client
participant "任务API" as api
participant "文件处理" as file
participant "时段计算" as slot
participant "XXL-JOB" as job
participant "任务执行" as exec
participant "结果处理" as result

== 任务提交阶段 ==
client -> api: 1.提交批量任务
api -> api: 2.创建任务实例
api -> file: 3.发送文件处理消息
api --> client: 4.返回任务ID

== 文件处理阶段 ==
file -> file: 5.分批解析文件
file -> file: 6.按business_key分表插入
file -> slot: 7.计算时段和并发
slot -> slot: 8.Redis锁控制
slot -> slot: 9.创建执行计划
slot -> result: 10.发送计划完成通知

== 任务执行阶段 ==
job -> exec: 11.定时触发执行（XXL-JOB任务预先创建）
exec -> exec: 12.查询待执行时段
exec -> exec: 13.批量执行任务
exec -> exec: 14.更新执行结果
exec -> result: 15.生成结果文件
result -> client: 16.发送结果通知

@enduml
```

**终止处理时序图**：
```plantuml
@startuml
participant "客户端" as client
participant "终止API" as api
participant "任务服务" as task
participant "终止处理" as terminate
participant "执行器" as executor
participant "结果处理" as result

== 终止请求阶段 ==
client -> api: 1.发送终止请求
api -> api: 2.获取任务类型和状态
api -> nacos: 3.获取terminate.supportAfterComplete配置

opt 任务已执行完成且supportAfterComplete=false
  api --> client: 返回执行完成后不支持终止
end

api -> api: 4.校验其他终止条件

api -> task: 5.更新任务状态为待终止
api -> terminate: 6.发送终止消息
api --> client: 7.返回终止受理结果

== 终止处理阶段 ==
terminate -> terminate: 8.查询执行成功的数据
terminate -> nacos: 9.获取terminate.needCallDownstream配置

alt needCallDownstream=true
  terminate -> executor: 10.批量调用下游终止接口
  executor -> executor: 11.处理终止逻辑
  executor -> terminate: 12.返回终止结果
  terminate -> terminate: 13.更新终止状态
else needCallDownstream=false
  terminate -> terminate: 14.批量标记为已终止
end

terminate -> result: 15.生成终止结果文件
result -> client: 16.发送终止完成通知

@enduml
```

## 4. 关键技术特性

### 4.1 参数解析与模板替换机制
**实现方式**：
- 参数优先级：个性化参数 > 通用参数 > 系统字段 > 表字段
- 使用${}占位符标记动态字段
- 支持嵌套JSON结构
- 支持默认值设置

**示例**：
```java
@Component
public class TemplateParameterResolver {

    public String resolveTemplate(String template, Long taskId, ExecutionDetail detail) {
        // 构建所有参数（按优先级）
        Map<String, Object> allParams = buildAllParams(taskId, detail);

        // 替换模板参数
        return replaceTemplateParams(template, allParams);
    }

    private Map<String, Object> buildAllParams(Long taskId, ExecutionDetail detail) {
        Map<String, Object> params = new HashMap<>();

        // 1. 表字段（最低优先级）
        params.put("taskId", taskId);
        params.put("msisdn", detail.getBusinessKey());
        params.put("messageId", taskId + "_" + detail.getId());

        // 2. 系统字段
        params.put("requestTime", LocalDateTime.now().format(DateTimeFormatter.ISO_LOCAL_DATE_TIME));
        params.put("systemCode", "QOS_BATCH");
        params.put("version", "1.0");

        // 3. 通用参数（任务主表）
        Map<String, Object> taskParams = getTaskParameters(taskId);
        if (taskParams != null) {
            params.putAll(taskParams);
        }

        // 4. 个性化参数（最高优先级）
        if (StringUtils.isNotBlank(detail.getPersonalizedData())) {
            Map<String, Object> personalizedParams = JsonUtil.string2Obj(detail.getPersonalizedData(), Map.class);
            params.putAll(personalizedParams);
        }

        return params;
    }

    private String replaceTemplateParams(String template, Map<String, Object> params) {
        String result = template;

        // 替换所有${key}占位符
        for (Map.Entry<String, Object> entry : params.entrySet()) {
            String placeholder = "${" + entry.getKey() + "}";
            String value = entry.getValue() != null ? String.valueOf(entry.getValue()) : "";
            result = result.replace(placeholder, value);
        }

        return result;
    }
}
```

### 4.2 分表查询策略
**实现方式**：
- 程序默认查询所有40张分表
- 基于任务ID进行分表路由（插入时）
- XXL-JOB按省创建任务，每个省独立调度

**示例**：
```java
@Component
public class ExecutionDetailService {

    private static final int TABLE_COUNT = 40;

    // 插入时的分表路由
    public String getTableNameForInsert(Long taskId) {
        int tableIndex = (int) (taskId % TABLE_COUNT);
        return "batch_execution_detail_" + String.format("%02d", tableIndex);
    }

    // 查询时默认查询所有表
    public List<ExecutionDetail> queryAllTables(Long taskId, Integer slotId, Integer status) {
        List<ExecutionDetail> result = new ArrayList<>();

        for (int i = 0; i < TABLE_COUNT; i++) {
            String tableName = "batch_execution_detail_" + String.format("%02d", i);
            List<ExecutionDetail> tableData = queryFromTable(tableName, taskId, slotId, status);
            result.addAll(tableData);
        }

        return result;
    }

    // 按分片查询（XXL-JOB分片使用）
    public List<ExecutionDetail> queryBySharding(Long taskId, int shardIndex, int shardTotal) {
        List<ExecutionDetail> result = new ArrayList<>();

        for (int i = 0; i < TABLE_COUNT; i++) {
            if (i % shardTotal == shardIndex) {
                String tableName = "batch_execution_detail_" + String.format("%02d", i);
                List<ExecutionDetail> tableData = queryFromTable(tableName, taskId, null, 0);
                result.addAll(tableData);
            }
        }

        return result;
    }
}
```

### 4.3 并发配置管理（独立配置）
**实现方式**：
- 从Nacos独立配置文件获取并发配置
- 支持按任务类型配置
- 支持按省配置和全局配置
- 支持配置热更新

**示例**：
```java
@Component
public class ConcurrencyConfigManager {

    @NacosValue("${batch-task.concurrency}")
    private String concurrencyConfig;

    public int getConcurrency(String taskType, String province) {
        // 从Nacos获取并发配置
        Map<String, Object> allConfig = parseConfig(concurrencyConfig);
        Map<String, Object> taskConfig = (Map<String, Object>) allConfig.get(taskType);

        if (taskConfig == null) {
            return 50; // 默认并发数
        }

        String type = (String) taskConfig.get("type");
        Map<String, Integer> config = (Map<String, Integer>) taskConfig.get("config");

        if ("province".equals(type) && province != null) {
            return config.getOrDefault(province, config.get("default"));
        } else {
            return config.get("default");
        }
    }
}
```

### 4.4 文件解析通用性设计（最终简化版）
**实现方式**：
- 每个Topic对应一个消费者，消费者直接指定解析器
- 解析器接口统一，通过工厂模式获取具体实现
- 配置中只需要指定Topic和文件列定义

**文件解析器接口**：
```java
public interface IFileParser {
    /**
     * 解析文件并返回执行明细列表
     */
    List<ExecutionDetail> parseFile(InputStream inputStream, Long taskId);
}

// 简单文件解析器（仅号码，分批处理）
@Component
public class SimpleFileParser implements IFileParser {

    @Autowired
    private FileProcessorService fileProcessorService;

    @Autowired
    private AsyncPropertiesConfig asyncPropertiesConfig;

    @Override
    public List<ExecutionDetail> parseFile(InputStream inputStream, Long taskId) {
        try (BufferedReader reader = new BufferedReader(new InputStreamReader(inputStream));
             CSVReader csvReader = new CSVReader(reader)) {

            String[] nextLine;
            int sequenceNumber = 0;
            int totalCount = 0;
            List<ExecutionDetail> batchDetails = new ArrayList<>();
            int batchSize = asyncPropertiesConfig.getBatchInsertCount();

            while ((nextLine = csvReader.readNext()) != null) {
                String msisdn = nextLine[0];
                if (MatcherCheck.invalidMsisdn(msisdn)) {
                    log.info("[{}]非手机号不处理", msisdn);
                    continue;
                }

                sequenceNumber++;

                ExecutionDetail detail = ExecutionDetail.builder()
                    .taskId(taskId)
                    .businessKey(msisdn)
                    .sequenceId(sequenceNumber)
                    .status(ExecutionStatus.PENDING.getCode())
                    .build();

                batchDetails.add(detail);

                // 达到批次大小时进行批量插入
                if (sequenceNumber % batchSize == 0) {
                    fileProcessorService.batchInsertDetailsByBusinessKey(batchDetails);
                    totalCount += batchDetails.size();
                    batchDetails.clear();
                    log.debug("批量插入号码，已插入至{}条", sequenceNumber);
                }
            }

            // 处理剩余的数据
            if (!batchDetails.isEmpty()) {
                fileProcessorService.batchInsertDetailsByBusinessKey(batchDetails);
                totalCount += batchDetails.size();
                batchDetails.clear();
                log.debug("最后剩余批量插入号码，已插入至{}条", sequenceNumber);
            }

            if (sequenceNumber == 0) {
                throw new DefBearerBatchException("文件无有效数据");
            }

            // 返回总数（用于后续时段计算）
            return List.of(ExecutionDetail.builder().taskId(taskId).sequenceId(totalCount).build());

        } catch (Exception e) {
            throw new FileParseException("简单文件解析失败", e);
        }
    }
}

// 默载调用文件解析器（多列，分批处理）
@Component
public class DefbearerCallFileParser implements IFileParser {

    @Autowired
    private FileProcessorService fileProcessorService;

    @Autowired
    private AsyncPropertiesConfig asyncPropertiesConfig;

    @Override
    public List<ExecutionDetail> parseFile(InputStream inputStream, Long taskId) {
        // 硬编码列定义：号码+私网IP（也可以从配置文件获取）
        String[] columns = {"msisdn", "privateIp"};

        try (BufferedReader reader = new BufferedReader(new InputStreamReader(inputStream));
             CSVReader csvReader = new CSVReader(reader)) {

            String[] nextLine;
            int sequenceNumber = 0;
            int totalCount = 0;
            List<ExecutionDetail> batchDetails = new ArrayList<>();
            int batchSize = asyncPropertiesConfig.getBatchInsertCount();

            while ((nextLine = csvReader.readNext()) != null) {
                if (nextLine.length < columns.length) {
                    log.warn("文件列数不足，跳过该行: {}", Arrays.toString(nextLine));
                    continue;
                }

                String msisdn = nextLine[0]; // 第一列固定为号码
                if (MatcherCheck.invalidMsisdn(msisdn)) {
                    log.info("[{}]非手机号不处理", msisdn);
                    continue;
                }

                sequenceNumber++;

                // 构建个性化参数
                Map<String, Object> personalizedData = new HashMap<>();
                for (int i = 1; i < columns.length; i++) {
                    personalizedData.put(columns[i], nextLine[i]);
                }

                ExecutionDetail detail = ExecutionDetail.builder()
                    .taskId(taskId)
                    .businessKey(msisdn)
                    .sequenceId(sequenceNumber)
                    .personalizedData(JsonUtil.obj2String(personalizedData))
                    .status(ExecutionStatus.PENDING.getCode())
                    .build();

                batchDetails.add(detail);

                // 达到批次大小时进行批量插入
                if (sequenceNumber % batchSize == 0) {
                    fileProcessorService.batchInsertDetailsByBusinessKey(batchDetails);
                    totalCount += batchDetails.size();
                    batchDetails.clear();
                    log.debug("批量插入号码，已插入至{}条", sequenceNumber);
                }
            }

            // 处理剩余的数据
            if (!batchDetails.isEmpty()) {
                fileProcessorService.batchInsertDetailsByBusinessKey(batchDetails);
                totalCount += batchDetails.size();
                batchDetails.clear();
                log.debug("最后剩余批量插入号码，已插入至{}条", sequenceNumber);
            }

            if (sequenceNumber == 0) {
                throw new DefBearerBatchException("文件无有效数据");
            }

            // 返回总数（用于后续时段计算）
            return List.of(ExecutionDetail.builder().taskId(taskId).sequenceId(totalCount).build());

        } catch (Exception e) {
            throw new FileParseException("默载调用文件解析失败", e);
        }
    }
}
```

### 4.5 消息处理设计（最终简化版）
**实现方式**：
- 文件处理消息生产者从Nacos获取Topic
- 每个消费者直接指定对应的文件解析器
- 其他消息都用通用Topic

**消息生产者**：
```java
// 文件解析消息生产者
@Component
public class FileProcessMessageProducer {

    public void sendFileProcessMessage(Long taskId, String taskType) {
        // 从Nacos配置获取Topic
        String topicName = nacosConfigService.getFileProcessTopic(taskType);

        FileProcessMessage message = FileProcessMessage.builder()
            .taskId(taskId)
            .taskType(taskType)
            .timestamp(System.currentTimeMillis())
            .build();

        messageProducer.sendMessageAsync(topicName, JsonUtil.obj2String(message));
    }
}
```

**消费者设计**：
```java
// 默载调用文件解析消费者（融合现有异常处理逻辑）
@KafkaListener(topics = "FILE_PROCESS_DEFBEARER_TOPIC", concurrency = "2")
@Component
public class DefbearerFileProcessConsumer {

    @Autowired
    private DefbearerCallFileParser fileParser; // 直接注入对应的解析器

    @Autowired
    private FileProcessorService fileProcessorService;

    @Autowired
    private TimeSlotService timeSlotService;

    @Autowired
    private MessageProducer messageProducer;

    public void processFileMessage(String message) {
        FileProcessMessage msg = JsonUtil.string2Obj(message, FileProcessMessage.class);

        try (InputStream inputStream = sftpService.getFile(getFilePath(msg.getTaskId()))) {
            log.info("开始处理任务[{}]文件解析，当前第{}次重试", msg.getTaskId(), msg.getRetryCount());

            // 直接使用指定的解析器（分批处理并插入）
            List<ExecutionDetail> result = fileParser.parseFile(inputStream, msg.getTaskId());

            if (result.isEmpty() || result.get(0).getSequenceId() == 0) {
                throw new DefBearerBatchException("文件无有效数据");
            }

            // 获取总数（解析器已经完成了分批插入）
            int totalCount = result.get(0).getSequenceId();

            // 计算时段和并发（独立服务处理）
            timeSlotService.calculateAndCreateTimeSlots(msg.getTaskId(), msg.getTaskType(), totalCount);

            log.info("任务[{}]文件处理完成，总条数: {}", msg.getTaskId(), details.size());

        } catch (DefBearerBatchException e) {
            log.error("任务[{}]业务处理失败: {}", msg.getTaskId(), e.getMessage());
            // 业务异常直接失败，进行结果上报
            handleBusinessError(msg, e);
        } catch (Exception e) {
            log.error("任务[{}]系统处理异常", msg.getTaskId(), e);
            // 系统异常进行重试
            handleSystemError(msg, e);
        }
    }

    private void handleBusinessError(FileProcessMessage msg, DefBearerBatchException e) {
        // 更新任务状态为解析失败
        taskService.updateTaskStatus(msg.getTaskId(), TaskStatus.PARSE_FAILED);

        // 发送失败通知
        messageProducer.sendMessageAsync("EXECUTE_RESULT_FILE_TOPIC",
            buildFailureNotification(msg.getTaskId(), e.getCode(), e.getMessage()));
    }

    private void handleSystemError(FileProcessMessage msg, Exception e) {
        // 系统异常进行重试
        MessageRetryBO retryMessage = MessageRetryBO.builder()
            .req(msg.getTaskId())
            .type(NotifyRetryTypeEnum.FILE_PROCESSOR_RETRY.getType())
            .retryCount(msg.getRetryCount() + 1)
            .topicName("FILE_PROCESS_DEFBEARER_TOPIC")
            .build();

        messageProducer.sendMessageAsync("MESSAGE_DEAL_FAIL_RETRY_TOPIC",
            JsonUtil.obj2String(retryMessage));

        log.info("发送文件解析失败重试消息，任务[{}]", msg.getTaskId());
    }
}

// 销户文件解析消费者
@KafkaListener(topics = "FILE_PROCESS_ACCOUNT_TOPIC", concurrency = "1")
@Component
public class AccountFileProcessConsumer {

    @Autowired
    private SimpleFileParser fileParser; // 直接注入简单解析器

    @Autowired
    private FileProcessorService fileProcessorService;

    public void processFileMessage(String message) {
        FileProcessMessage msg = JsonUtil.string2Obj(message, FileProcessMessage.class);

        try (InputStream inputStream = sftpService.getFile(getFilePath(msg.getTaskId()))) {
            log.info("开始处理任务[{}]文件解析，当前第{}次重试", msg.getTaskId(), msg.getRetryCount());

            // 直接使用简单解析器（分批处理并插入）
            List<ExecutionDetail> result = fileParser.parseFile(inputStream, msg.getTaskId());

            if (result.isEmpty() || result.get(0).getSequenceId() == 0) {
                throw new DefBearerBatchException("文件无有效数据");
            }

            // 获取总数（解析器已经完成了分批插入）
            int totalCount = result.get(0).getSequenceId();

            // 计算时段和并发（独立服务处理）
            timeSlotService.calculateAndCreateTimeSlots(msg.getTaskId(), msg.getTaskType(), totalCount);

            log.info("任务[{}]文件处理完成，总条数: {}", msg.getTaskId(), totalCount);

        } catch (DefBearerBatchException e) {
            log.error("任务[{}]业务处理失败: {}", msg.getTaskId(), e.getMessage());
            // 业务异常直接失败，进行结果上报
            handleBusinessError(msg, e);
        } catch (Exception e) {
            log.error("任务[{}]系统处理异常", msg.getTaskId(), e);
            // 系统异常进行重试
            handleSystemError(msg, e);
        }
    }
}
```

**通用文件处理服务（按business_key分表）**：
```java
@Component
public class FileProcessorService {

    @Autowired
    private ExecutionDetailMapper executionDetailMapper;

    public void batchInsertDetailsByBusinessKey(List<ExecutionDetail> details) {
        // 按business_key进行分表策略分组
        Map<String, List<ExecutionDetail>> tableGroups = details.stream()
            .collect(Collectors.groupingBy(detail -> getTableNameByBusinessKey(detail.getBusinessKey())));

        // 分表批量插入
        for (Map.Entry<String, List<ExecutionDetail>> entry : tableGroups.entrySet()) {
            String tableName = entry.getKey();
            List<ExecutionDetail> tableDetails = entry.getValue();
            executionDetailMapper.batchInsert(tableName, tableDetails);
        }
    }

    private String getTableNameByBusinessKey(String businessKey) {
        // 使用business_key（手机号）的hash值进行分表
        int tableIndex = Math.abs(businessKey.hashCode()) % 40;
        return "batch_execution_detail_" + String.format("%02d", tableIndex);
    }
}
```

**时段计算服务（独立服务，融合现有逻辑）**：
```java
@Component
public class TimeSlotService {

    @Autowired
    private ConcurrencyConfigManager concurrencyConfigManager;

    @Autowired
    private ExecutionPlanMapper executionPlanMapper;

    @Autowired
    private XxlJobTaskManager xxlJobTaskManager;

    @Autowired
    private RedissonClient redissonClient;

    @Autowired
    private AsyncPropertiesConfig asyncPropertiesConfig;

    public void calculateAndCreateTimeSlots(Long taskId, String taskType, int totalCount) {
        // 获取任务信息
        BatchTaskInstance task = taskService.getTaskById(taskId);
        Integer homeProvince = task.getHomeProvince();

        // 获取并发配置类型，决定锁策略
        ConcurrencyConfig concurrencyConfig = concurrencyConfigManager.getConcurrencyConfig(taskType);
        String lockKey = buildLockKey(concurrencyConfig.getType(), taskType, homeProvince);
        RLock lock = redissonClient.getLock(lockKey);
        boolean lockSuccess = false;

        try {
            // 尝试获取锁
            lockSuccess = lock.tryLock(asyncPropertiesConfig.getLock().getWaitMillis(), TimeUnit.SECONDS);
            if (!lockSuccess) {
                throw new BatchSystemInternalException("未获取到省份锁，需要重试");
            }

            // 计算开始时间
            LocalDateTime startTime = calculateStartTime(task.getExpectStartTime());

            // 获取可执行时段（检查时段交叉）
            TreeMap<LocalDateTime, Integer> timeSlots = getExecutableTimeSlots(concurrencyConfig, homeProvince, startTime, totalCount, taskType);

            // 检查预期结束时间
            if (task.getExpectEndTime() != null) {
                LocalDateTime calculatedEndTime = timeSlots.lastKey().plusMinutes(asyncPropertiesConfig.getSingleTimeslots());
                if (task.getExpectEndTime().isBefore(calculatedEndTime)) {
                    throw new DefBearerBatchException("计算的结束时间超过了预期结束时间");
                }
            }

            // 转换为执行计划
            List<ExecutionPlan> plans = convertToExecutionPlans(taskId, homeProvince, timeSlots);

            // 插入执行计划
            executionPlanMapper.batchInsert(plans);

            // 发送计划完成通知
            messageProducer.sendMessageAsync("EXECUTE_RESULT_FILE_TOPIC",
                buildPlanCompleteMessage(taskId, taskType));

        } catch (Exception e) {
            log.error("时段计算失败", e);
            throw e;
        } finally {
            if (lockSuccess) {
                lock.unlock();
                log.info("释放省份锁: {}", lockKey);
            }
        }
    }

    private String buildLockKey(String concurrencyType, String taskType, Integer homeProvince) {
        // 统一锁名格式：batch:lock:任务类型:省分编码
        String lockKey = "batch:lock:" + taskType;

        if ("province".equals(concurrencyType)) {
            if (homeProvince != null) {
                // 按省配置且有省分编码：锁名包含省分编码
                lockKey += ":" + homeProvince;
            } else {
                // 按省配置但homeProvince为空：使用特殊标识
                lockKey += ":no_province";
            }
        } else {
            // 全局配置：锁名不包含省分编码
            lockKey += ":global";
        }

        return lockKey;
    }

    private TreeMap<LocalDateTime, Integer> getExecutableTimeSlots(ConcurrencyConfig concurrencyConfig,
                                                                   Integer homeProvince, LocalDateTime startTime,
                                                                   int totalCount, String taskType) {
        while (true) {
            // 计算时段数
            TreeMap<LocalDateTime, Integer> timeSlots = calculateTimeSlots(concurrencyConfig, homeProvince, startTime, totalCount, taskType);
            LocalDateTime endTime = timeSlots.lastKey().plusMinutes(asyncPropertiesConfig.getSingleTimeslots());

            // 判断是否存在交叉时段（根据并发类型决定查询条件）
            LocalDateTime intersectMaxEndTime;
            if ("province".equals(concurrencyConfig.getType())) {
                // 按省配置：只检查同省的时段交叉
                intersectMaxEndTime = executionPlanMapper.getIntersectMaxEndTimeByProvince(homeProvince, startTime, endTime);
            } else {
                // 全局配置：检查所有任务类型的时段交叉
                intersectMaxEndTime = executionPlanMapper.getIntersectMaxEndTimeByTaskType(taskType, startTime, endTime);
            }

            if (intersectMaxEndTime != null) {
                // 重新计算获取时段
                startTime = intersectMaxEndTime;
                continue;
            }
            return timeSlots;
        }
    }

    private TreeMap<LocalDateTime, Integer> calculateTimeSlots(ConcurrencyConfig concurrencyConfig, Integer homeProvince,
                                                               LocalDateTime startTime, int totalCount, String taskType) {
        // 获取并发配置
        int concurrency;
        if ("province".equals(concurrencyConfig.getType())) {
            Map<String, Integer> provinceConfig = concurrencyConfig.getProvinceConfig();
            if (homeProvince != null) {
                // 按省配置且有省分编码
                concurrency = provinceConfig.getOrDefault(
                    homeProvince.toString(),
                    provinceConfig.get("default"));
            } else {
                // 按省配置但homeProvince为空，优先使用no_province配置
                concurrency = provinceConfig.getOrDefault(
                    "no_province",
                    provinceConfig.get("default"));
            }
        } else {
            // 全局配置
            Map<String, Integer> globalConfig = concurrencyConfig.getGlobalConfig();
            concurrency = globalConfig.get("default");
        }

        // 每个时段最大执行数量（按秒计算）
        int slotDuration = asyncPropertiesConfig.getSingleTimeslots(); // 分钟
        int maxExecCount = concurrency * slotDuration * 60; // 转换为秒

        // 计算时段
        TreeMap<LocalDateTime, Integer> timeSlots = new TreeMap<>();
        while (totalCount > 0) {
            int currentSlotCount = Math.min(totalCount, maxExecCount);
            timeSlots.put(startTime, currentSlotCount);
            totalCount -= currentSlotCount;
            startTime = startTime.plusMinutes(slotDuration);
        }

        return timeSlots;
    }

    private LocalDateTime calculateStartTime(LocalDateTime expectStartTime) {
        // 与当前时间比较，取较大值
        LocalDateTime sourceDateTime = expectStartTime.isBefore(LocalDateTime.now()) ?
            LocalDateTime.now() : expectStartTime;

        // 计算下一个时段的开始时间
        LocalDateTime targetDateTime = sourceDateTime.plusMinutes(1);
        int currentMinute = targetDateTime.getMinute();
        int slotDuration = asyncPropertiesConfig.getSingleTimeslots();
        int nextMultipleOfMinute = (currentMinute / slotDuration + 1) * slotDuration;

        if (nextMultipleOfMinute == 60) {
            targetDateTime = targetDateTime.plusHours(1).withMinute(0).withSecond(0).withNano(0);
        } else {
            targetDateTime = targetDateTime.withMinute(nextMultipleOfMinute).withSecond(0).withNano(0);
        }

        return targetDateTime;
    }

    private List<ExecutionPlan> convertToExecutionPlans(Long taskId, Integer homeProvince,
                                                        TreeMap<LocalDateTime, Integer> timeSlots) {
        List<ExecutionPlan> plans = new ArrayList<>();
        int slotId = 1;

        for (Map.Entry<LocalDateTime, Integer> entry : timeSlots.entrySet()) {
            LocalDateTime startTime = entry.getKey();
            Integer executionCount = entry.getValue();

            ExecutionPlan plan = ExecutionPlan.builder()
                .taskId(taskId)
                .slotId(slotId)
                .homeProvince(homeProvince)
                .slotStartTime(startTime)
                .slotEndTime(startTime.plusMinutes(asyncPropertiesConfig.getSingleTimeslots()))
                .plannedCount(executionCount)
                .status(ExecutionStatus.PENDING.getCode())
                .build();

            plans.add(plan);
            slotId++;
        }

        return plans;
    }
}
```

### 4.6 差异化终止处理
**实现方式**：
- 终止接口统一校验逻辑
- 根据Nacos配置决定是否调用下游
- 支持自定义终止策略

**示例**：
```java
@RestController
public class BatchTaskTerminateController {

    @PostMapping("/terminate")
    public Resp<Void> terminate(@RequestBody TerminateReq req) {
        // 统一的终止校验逻辑
        TerminateValidationResult validation = validateTerminate(req.getTaskId());
        if (!validation.isValid()) {
            return Resp.fail(validation.getErrorMessage());
        }

        // 获取任务类型
        String taskType = taskService.getTaskType(req.getTaskId());

        // 获取终止配置
        TerminateConfig config = nacosConfigService.getTerminateConfig(taskType);

        if (config.isNeedCallDownstream()) {
            // 需要调用下游终止接口
            terminateService.terminateWithDownstream(req.getTaskId(), taskType);
        } else {
            // 仅标记终止
            terminateService.markTerminate(req.getTaskId());
        }

        return Resp.success();
    }

    private TerminateValidationResult validateTerminate(Long taskId) {
        // 自定义校验逻辑
        TaskStatus status = taskService.getTaskStatus(taskId);
        if (status == TaskStatus.ALL_COMPLETED) {
            return TerminateValidationResult.invalid("任务已全部完成，不支持终止");
        }

        // 检查是否有执行成功的数据支持终止
        boolean hasSuccessData = executionDetailService.hasSuccessData(taskId);
        if (!hasSuccessData) {
            return TerminateValidationResult.invalid("无执行成功数据，无需终止");
        }

        return TerminateValidationResult.valid();
    }
}
```

## 5. 核心接口设计

### 5.1 执行器接口
```java
@BatchTaskExecutor(taskType = "batch_defbearer_call")
@Component
public class BatchDefbearerCallExecutor {

    public void execute(ExecuteContext context) {
        // 获取执行模板
        String executeTemplate = nacosConfigService.getExecuteTemplate(context.getTaskType());

        // 批量处理数据
        for (ExecutionDetail detail : context.getBatchData()) {
            try {
                // 替换模板参数
                Map<String, Object> params = buildParams(detail, context);
                String requestJson = jsonTemplateResolver.resolveTemplate(executeTemplate, params);

                // 调用下游接口
                Map<String, Object> request = JsonUtil.string2Obj(requestJson, Map.class);
                Resp<Object> response = callDownstreamService(request);

                // 更新执行结果（分表更新）
                updateExecutionResult(detail, response);

            } catch (Exception e) {
                log.error("执行失败", e);
                updateExecutionError(detail, e);
            }
        }
    }

    private Map<String, Object> buildParams(ExecutionDetail detail, ExecuteContext context) {
        Map<String, Object> params = new HashMap<>();
        params.put("msisdn", detail.getBusinessKey());
        params.put("requestTime", LocalDateTime.now().format(DateTimeFormatter.ISO_LOCAL_DATE_TIME));

        // 添加任务参数
        if (context.getTaskParameters() != null) {
            params.putAll(context.getTaskParameters());
        }

        // 添加输入数据
        if (detail.getInputData() != null) {
            params.putAll(JsonUtil.string2Obj(detail.getInputData(), Map.class));
        }

        return params;
    }
}
```

### 5.2 终止器接口
```java
@BatchTaskTerminator(taskType = "batch_defbearer_call")
@Component
public class BatchDefbearerCallTerminator {

    public void terminate(TerminateContext context) {
        // 获取终止配置
        TerminateConfig config = nacosConfigService.getTerminateConfig(context.getTaskType());

        if (config.isNeedCallDownstream()) {
            // 获取终止模板
            String terminateTemplate = config.getTerminateTemplate();

            // 查询执行成功的数据（查询所有分表）
            List<ExecutionDetail> successDetails = executionDetailService.queryAllTables(
                context.getTaskId(), null, ExecutionStatus.SUCCESS.getCode());

            // 处理执行成功的数据
            for (ExecutionDetail detail : successDetails) {
                try {
                    // 替换终止模板参数
                    Map<String, Object> params = buildTerminateParams(detail, context);
                    String requestJson = jsonTemplateResolver.resolveTemplate(terminateTemplate, params);

                    // 调用下游终止接口
                    Map<String, Object> request = JsonUtil.string2Obj(requestJson, Map.class);
                    Resp<Object> response = callDownstreamTerminateService(request);

                    // 更新终止结果（分表更新）
                    updateTerminateResult(detail, response);

                } catch (Exception e) {
                    log.error("终止失败", e);
                    updateTerminateError(detail, e);
                }
            }
        } else {
            // 仅标记终止，不调用下游（批量更新所有分表）
            executionDetailService.batchUpdateTerminateStatus(context.getTaskId());
        }
    }

    private Map<String, Object> buildTerminateParams(ExecutionDetail detail, TerminateContext context) {
        Map<String, Object> params = new HashMap<>();
        params.put("msisdn", detail.getBusinessKey());
        params.put("requestTime", LocalDateTime.now().format(DateTimeFormatter.ISO_LOCAL_DATE_TIME));

        // 从输出数据中获取订单号等信息
        if (detail.getOutputData() != null) {
            Map<String, Object> outputData = JsonUtil.string2Obj(detail.getOutputData(), Map.class);
            params.put("orderId", outputData.get("orderId"));
        }

        return params;
    }
}
```

## 7. 实施建议

### 7.1 分阶段实施
**第一阶段**：核心框架搭建
- Nacos模板配置管理
- 执行器注册机制
- 分表路由策略
- JSON模板解析器

**第二阶段**：执行引擎优化
- XXL-JOB分片集成
- 并发配置管理
- 差异化终止机制

**第三阶段**：高级特性
- 监控告警完善
- 性能优化
- 可视化配置界面

### 7.2 技术栈选择
- **基础框架**：Spring Boot 3.x + MyBatis
- **配置中心**：Nacos 2.x
- **任务调度**：XXL-JOB 2.x
- **缓存存储**：Redis 6.x
- **消息队列**：Kafka 2.x
- **数据库**：MySQL 8.x
- **监控**：Micrometer + Prometheus + Grafana

## 6. 状态机设计

### 6.1 任务实例状态机

```plantuml
@startuml
!define RECTANGLE class

[*] --> 已接收请求待解析文件 : 创建任务
已接收请求待解析文件 --> 解析失败 : 文件解析失败
已接收请求待解析文件 --> 待执行 : 文件解析成功
待执行 --> 执行完成待反馈执行结果 : 全部执行完成
待执行 --> 待终止 : 收到终止请求
执行完成待反馈执行结果 --> 已执行 : 结果文件生成完成
待终止 --> 终止完成待反馈终止结果 : 终止处理完成
终止完成待反馈终止结果 --> 已终止 : 终止结果文件生成完成
解析失败 --> [*]
已执行 --> [*]
已终止 --> [*]

@enduml
```

**任务实例状态说明**：
- **已接收请求待解析文件(0)**：任务刚创建，正在进行文件解析和时段计算
- **待执行(1)**：文件解析完成，等待定时执行
- **执行完成待反馈执行结果(2)**：所有任务执行完成，结果文件已生成，正在调用上游接口反馈结果，等待接口返回
- **已执行(3)**：上游接口反馈成功，任务完全结束
- **待终止(4)**：收到终止请求，正在进行终止处理
- **终止完成待反馈终止结果(5)**：终止处理完成，终止结果文件已生成，正在调用上游接口反馈终止结果，等待接口返回
- **已终止(6)**：上游接口反馈终止结果成功，任务完全结束
- **解析失败(7)**：文件解析失败，任务结束

### 6.2 执行计划状态机

```plantuml
@startuml
!define RECTANGLE class

[*] --> 待执行 : 创建执行计划
待执行 --> 执行中 : XXL-JOB触发执行
执行中 --> 已完成 : 时段执行完成
执行中 --> 已终止 : 收到终止指令
待执行 --> 已终止 : 收到终止指令
已完成 --> [*]
已终止 --> [*]

@enduml
```

**执行计划状态说明**：
- **待执行(0)**：时段计划已创建，等待执行时间到达
- **执行中(1)**：时段正在执行中
- **已完成(2)**：时段内所有任务执行完成
- **已终止(3)**：时段被终止执行

### 6.3 执行明细状态机

```plantuml
@startuml
!define RECTANGLE class

[*] --> 待执行 : 文件解析创建
待执行 --> 执行成功 : 调用下游成功
待执行 --> 执行失败 : 调用下游失败
执行成功 --> 终止成功 : 终止处理成功
执行成功 --> 终止失败 : 终止处理失败
执行成功 --> [*]
执行失败 --> [*]
待执行 --> [*]
终止成功 --> [*]
终止失败 --> [*]

note right of 待执行 : 终止时保持原状态\n减少无效数据库交互
note right of 执行失败 : 终止时保持原状态\n失败数据无需处理

@enduml
```

**执行明细状态说明**：
- **待执行(0)**：明细记录已创建，等待执行（终止时保持此状态，不变更）
- **执行成功(1)**：调用下游接口成功
- **执行失败(2)**：调用下游接口失败（终止时保持此状态，不变更）
- **终止成功(3)**：终止处理成功（仅执行成功的数据调用下游终止接口成功后变更为此状态）
- **终止失败(4)**：终止处理失败（仅执行成功的数据调用下游终止接口失败后变更为此状态）

---

**总结**：优化后的方案采用Nacos配置管理，简化了模板设计，使用JSON模板+${}占位符的方式更加直观易用。支持差异化终止处理，分表分片策略，既保持了现有架构优势，又大大提升了通用性和扩展性。
